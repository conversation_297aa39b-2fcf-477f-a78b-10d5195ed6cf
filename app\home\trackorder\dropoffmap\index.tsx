import * as Location from "expo-location";
import BackArrow from "../../../../assets/icons/BackArrow.svg";
import DirectionsIcon from "../../../../assets/Hometab/ic_outline-directions.svg";
import DotIcon from "../../../../assets/Hometab/doticon.svg";
import HeadSetIcon from "../../../../assets/Hometab/lucide_headset.svg";
import LocationIcon from "../../../../assets/Hometab/gridicons_location.svg";
import MapView, { Marker, <PERSON>yline } from "react-native-maps";
import MessageIcon from "../../../../assets/Hometab/ic_round-message.svg";
import PhoneIcon from "../../../../assets/Hometab/mingcute_phone-fill.svg";
import PopUpComponent from "../../../../component/PopUpModle/PopUpComponent";
import React, { useEffect, useRef, useState } from "react";
import VegIcon from "../../../../assets/Hometab/mdi_lacto-vegetarian.svg";
import { router, useLocalSearchParams } from "expo-router";
import { SafeAreaView } from "react-native-safe-area-context";

import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  Linking,
  Platform,
} from "react-native";

const index = () => {
  const [region, setRegion] = useState<any>(null);
  const [userLocation, setUserLocation] = useState<any>(null);
  const [routeCoordinates, setRouteCoordinates] = useState<any[]>([]);
  const [showDirections, setShowDirections] = useState(false);
  const [routeDistance, setRouteDistance] = useState<string>("");
  const [routeDuration, setRouteDuration] = useState<string>("");
  const [routeType, setRouteType] = useState<string>("");
  const mapRef = useRef<MapView>(null);
  const [showissueMenu, setshowissueMenu] = useState(false);

  const {
    user_Id,
    address,
    receiver_name,
    invoice_no,
    orderNumber,
    latitude,
    longitude,
  } = useLocalSearchParams();

  // Convert string coordinates to numbers with validation
  const destinationLat = latitude ? parseFloat(latitude as string) : 0;
  const destinationLng = longitude ? parseFloat(longitude as string) : 0;

  // Validate coordinates
  const isValidCoordinates =
    !isNaN(destinationLat) &&
    !isNaN(destinationLng) &&
    destinationLat !== 0 &&
    destinationLng !== 0;

  // Debug log for coordinate validation
  console.log("Dropoff Coordinate Debug:", {
    latitude: latitude,
    longitude: longitude,
    destinationLat,
    destinationLng,
    isValidCoordinates,
  });

  const requestLocationPermission = async () => {
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permission denied",
        "Allow location access to use this feature."
      );
      return;
    }

    try {
      let location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      // Store user location
      setUserLocation({ latitude, longitude });

      // Create region based on whether we have valid destination coordinates
      let newRegion;

      if (isValidCoordinates) {
        // Create region that includes both user location and destination
        const midLat = (latitude + destinationLat) / 2;
        const midLng = (longitude + destinationLng) / 2;
        const latDelta = Math.abs(latitude - destinationLat) * 1.5;
        const lngDelta = Math.abs(longitude - destinationLng) * 1.5;

        newRegion = {
          latitude: midLat,
          longitude: midLng,
          latitudeDelta: Math.max(latDelta, 0.01),
          longitudeDelta: Math.max(lngDelta, 0.01),
        };
      } else {
        // Just center on user location if destination coordinates are invalid
        newRegion = {
          latitude,
          longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
      }

      setRegion(newRegion);
      if (mapRef.current) {
        mapRef.current.animateToRegion(newRegion, 1000);
      }
    } catch (error) {
      console.error("Error getting location:", error);
      Alert.alert("Location Error", "Unable to get your current location.");
    }
  };

  // Function to calculate distance between two coordinates
  const calculateDistance = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number
  ) => {
    const R = 6371; // Radius of the Earth in kilometers
    const dLat = ((lat2 - lat1) * Math.PI) / 180;
    const dLon = ((lon2 - lon1) * Math.PI) / 180;
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos((lat1 * Math.PI) / 180) *
        Math.cos((lat2 * Math.PI) / 180) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c; // Distance in kilometers
    return distance;
  };

  // Function to decode Google polyline
  const decodePolyline = (encoded: string) => {
    const points = [];
    let index = 0;
    const len = encoded.length;
    let lat = 0;
    let lng = 0;

    while (index < len) {
      let b;
      let shift = 0;
      let result = 0;
      do {
        b = encoded.charAt(index++).charCodeAt(0) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlat = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.charAt(index++).charCodeAt(0) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      const dlng = (result & 1) !== 0 ? ~(result >> 1) : result >> 1;
      lng += dlng;

      points.push({
        latitude: lat / 1e5,
        longitude: lng / 1e5,
      });
    }

    return points;
  };

  // Generate a more realistic route that follows road patterns
  const generateRealisticRoute = (origin: any, destination: any) => {
    const points = [];
    const startLat = origin.latitude;
    const startLng = origin.longitude;
    const endLat = destination.latitude;
    const endLng = destination.longitude;

    // Calculate the distance and direction
    const latDiff = endLat - startLat;
    const lngDiff = endLng - startLng;
    const distance = Math.sqrt(latDiff * latDiff + lngDiff * lngDiff);

    // Create intermediate points that simulate road patterns
    // Limit points to prevent memory issues - max 20 points for safety
    const numPoints = Math.min(20, Math.max(5, Math.floor(distance * 50)));
    console.log(
      `Generating dropoff route with ${numPoints} points for distance ${distance.toFixed(
        4
      )}`
    );

    for (let i = 0; i <= numPoints; i++) {
      const ratio = i / numPoints;

      // Base interpolation
      let lat = startLat + latDiff * ratio;
      let lng = startLng + lngDiff * ratio;

      // Add road-like variations (simulate following streets)
      if (i > 0 && i < numPoints) {
        // Add slight curves to simulate road patterns (reduced complexity)
        const curveOffset = Math.sin(ratio * Math.PI * 2) * distance * 0.05;

        // Simple perpendicular offset for curves
        if (distance > 0) {
          const perpendicularLat = -lngDiff / distance;
          const perpendicularLng = latDiff / distance;

          lat += perpendicularLat * curveOffset;
          lng += perpendicularLng * curveOffset;
        }

        // Simplified grid-like adjustments
        if (Math.abs(latDiff) > Math.abs(lngDiff)) {
          // Primarily north-south movement
          if (i < numPoints * 0.25) {
            lng = startLng + lngDiff * 0.05;
          } else if (i > numPoints * 0.75) {
            lng = endLng - lngDiff * 0.05;
          }
        } else {
          // Primarily east-west movement
          if (i < numPoints * 0.25) {
            lat = startLat + latDiff * 0.05;
          } else if (i > numPoints * 0.75) {
            lat = endLat - latDiff * 0.05;
          }
        }
      }

      points.push({ latitude: lat, longitude: lng });
    }

    console.log(`Generated ${points.length} dropoff route points`);

    // Safety check - if somehow we have too many points, reduce them
    if (points.length > 25) {
      console.warn(`Too many points (${points.length}), reducing to 25`);
      const step = Math.floor(points.length / 25);
      const reducedPoints = points.filter((_, index) => index % step === 0);
      // Always include the last point
      if (
        reducedPoints[reducedPoints.length - 1] !== points[points.length - 1]
      ) {
        reducedPoints.push(points[points.length - 1]);
      }
      return reducedPoints;
    }

    return points;
  };

  // Simple route generation as backup
  const generateSimpleRoute = (origin: any, destination: any) => {
    console.log("🔄 Generating simple curved dropoff route...");

    const points = [];
    const startLat = origin.latitude;
    const startLng = origin.longitude;
    const endLat = destination.latitude;
    const endLng = destination.longitude;

    // Create only 8 points for a simple curved route
    for (let i = 0; i <= 8; i++) {
      const ratio = i / 8;
      let lat = startLat + (endLat - startLat) * ratio;
      let lng = startLng + (endLng - startLng) * ratio;

      // Add a simple curve in the middle
      if (i > 1 && i < 7) {
        const curveAmount = Math.sin(ratio * Math.PI) * 0.001; // Very small curve
        lat += curveAmount;
        lng += curveAmount;
      }

      points.push({ latitude: lat, longitude: lng });
    }

    return points;
  };

  // Alternative routing function
  const getAlternativeRoute = async (origin: any, destination: any) => {
    let finalRoutePoints;

    try {
      console.log("🔄 Generating realistic dropoff route pattern...");

      // Generate a more realistic route
      finalRoutePoints = generateRealisticRoute(origin, destination);

      console.log(
        "✅ Generated realistic dropoff route with",
        finalRoutePoints.length,
        "points"
      );
    } catch (error) {
      console.log(
        "❌ Complex dropoff route failed, using simple route:",
        error
      );

      // Fallback to simple route
      finalRoutePoints = generateSimpleRoute(origin, destination);
      console.log(
        "✅ Generated simple dropoff route with",
        finalRoutePoints.length,
        "points"
      );
    }

    setRouteCoordinates(finalRoutePoints);

    // Calculate distance and time based on the generated route
    let totalDistance = 0;
    for (let i = 1; i < finalRoutePoints.length; i++) {
      const segmentDistance = calculateDistance(
        finalRoutePoints[i - 1].latitude,
        finalRoutePoints[i - 1].longitude,
        finalRoutePoints[i].latitude,
        finalRoutePoints[i].longitude
      );
      totalDistance += segmentDistance;
    }

    const estimatedTime = Math.round((totalDistance / 35) * 60); // 35 km/h for city driving

    setRouteDistance(`${totalDistance.toFixed(1)} km`);
    setRouteDuration(`${estimatedTime} min`);
    setRouteType("Road-like Route");
    setShowDirections(true);
  };

  // Function to get directions with Google Directions API
  const getDirections = async () => {
    if (!userLocation) {
      Alert.alert(
        "Location not available",
        "Please wait for location to be detected."
      );
      return;
    }

    if (!isValidCoordinates) {
      Alert.alert(
        "Invalid destination",
        "Destination coordinates are not available."
      );
      return;
    }

    // Calculate straight-line distance for fallback
    const fallbackDistance = calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      destinationLat,
      destinationLng
    );

    const fallbackTime = Math.round((fallbackDistance / 40) * 60); // Assuming 40 km/h average speed

    // First try the realistic route generation (more reliable)
    try {
      console.log("🚀 Generating road-like dropoff route...");
      await getAlternativeRoute(userLocation, {
        latitude: destinationLat,
        longitude: destinationLng,
      });
      return; // Success with realistic route
    } catch (altError) {
      console.log(
        "❌ Realistic dropoff route generation failed:",
        altError instanceof Error ? altError.message : String(altError)
      );
    }

    // Final fallback to straight line route
    console.log("⚠️ Using straight line fallback route for dropoff");
    setRouteCoordinates([
      { latitude: userLocation.latitude, longitude: userLocation.longitude },
      { latitude: destinationLat, longitude: destinationLng },
    ]);

    setRouteDistance(`${fallbackDistance.toFixed(1)} km`);
    setRouteDuration(`${fallbackTime} min`);
    setRouteType("Straight Line");
    setShowDirections(true);
  };

  // Function to open external navigation apps
  const openExternalNavigation = async () => {
    if (!isValidCoordinates) {
      Alert.alert(
        "Invalid destination",
        "Destination coordinates are not available."
      );
      return;
    }

    const destination = `${destinationLat},${destinationLng}`;
    const label = encodeURIComponent(
      (receiver_name as string) || "Dropoff Location"
    );

    // Different URL schemes for different platforms and apps
    const schemes = {
      googleMaps: Platform.select({
        ios: `comgooglemaps://?daddr=${destination}&directionsmode=driving`,
        android: `google.navigation:q=${destination}&mode=d`,
      }),
      appleMaps: `maps://app?daddr=${destination}&dirflg=d`,
      waze: `waze://?ll=${destination}&navigate=yes`,
      // Fallback to web URLs
      googleMapsWeb: `https://www.google.com/maps/dir/?api=1&destination=${destination}&travelmode=driving`,
      appleMapsWeb: `http://maps.apple.com/?daddr=${destination}&dirflg=d`,
    };

    // Function to try opening a URL
    const tryOpenURL = async (url: string, appName: string) => {
      try {
        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
          return true;
        }
        return false;
      } catch (error) {
        console.log(`Failed to open ${appName}:`, error);
        return false;
      }
    };

    // Show options to user
    Alert.alert("Open Navigation", "Choose your preferred navigation app:", [
      {
        text: "Google Maps",
        onPress: async () => {
          const opened = await tryOpenURL(schemes.googleMaps!, "Google Maps");
          if (!opened) {
            // Fallback to web version
            await tryOpenURL(schemes.googleMapsWeb, "Google Maps Web");
          }
        },
      },
      ...(Platform.OS === "ios"
        ? [
            {
              text: "Apple Maps",
              onPress: async () => {
                const opened = await tryOpenURL(
                  schemes.appleMaps,
                  "Apple Maps"
                );
                if (!opened) {
                  await tryOpenURL(schemes.appleMapsWeb, "Apple Maps Web");
                }
              },
            },
          ]
        : []),
      {
        text: "Waze",
        onPress: async () => {
          const opened = await tryOpenURL(schemes.waze, "Waze");
          if (!opened) {
            Alert.alert(
              "Waze not installed",
              "Please install Waze app or use Google Maps instead."
            );
          }
        },
      },
      {
        text: "Cancel",
        style: "cancel",
      },
    ]);
  };

  useEffect(() => {
    requestLocationPermission();
  }, []);

  const [modle, setmodle] = useState(false);

  return (
    <SafeAreaView className="flex-1 justify-between">
      <ScrollView>
        <View className="px-4 mt-3">
          <View className="flex-row relative items-center justify-start mt-4">
            <TouchableOpacity
              onPress={() => {
                router.back();
              }}
              className="absolute"
            >
              <View>
                <BackArrow />
              </View>
            </TouchableOpacity>
            <View className="flex-1 items-center justify-center">
              <Text className="font-[400] text-[19px] leading-[39px]">
                Go to Dropoff
              </Text>
              <Text className="">Order ID: {orderNumber}</Text>
              {userLocation && (
                <TouchableOpacity
                  onPress={getDirections}
                  className="mt-2 bg-[#00660A] px-3 py-1 rounded-full"
                >
                  <Text className="text-white text-[12px] font-semibold">
                    Show Route
                  </Text>
                </TouchableOpacity>
              )}
            </View>
            <View className="relative">
              <TouchableOpacity
                onPress={() => {
                  setshowissueMenu(!showissueMenu);
                }}
              >
                <DotIcon />
              </TouchableOpacity>
              {showissueMenu && (
                <TouchableOpacity
                  onPress={() => {
                    router.push("/home/<USER>/pickupmap/issue");
                  }}
                  className="right-3 z-10 top-6 absolute flex-row space-x-2 h-[45px] w-[138px] items-center justify-center bg-[#000] rounded-[8px]"
                >
                  <HeadSetIcon />
                  <Text className="font-[400] font-Pop text-[#fff] leading-[21px] text-[14px]">
                    Report issue
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </View>
        <View className="relative h-[400px] mt-7">
          <MapView
            style={StyleSheet.absoluteFill}
            region={region}
            provider={"google"}
            ref={mapRef}
            showsUserLocation
            zoomEnabled={true}
            onRegionChangeComplete={setRegion}
            key={1}
          >
            {/* Destination Marker - only show if coordinates are valid */}
            {isValidCoordinates && (
              <Marker
                coordinate={{
                  latitude: destinationLat,
                  longitude: destinationLng,
                }}
                title={receiver_name as string}
                description={address as string}
                pinColor="red"
              />
            )}

            {/* Route Polyline */}
            {showDirections && routeCoordinates.length > 0 && (
              <Polyline
                coordinates={routeCoordinates}
                strokeColor="#00660A"
                strokeWidth={4}
                lineCap="round"
                lineJoin="round"
              />
            )}
          </MapView>

          {/* Route Information Overlay */}
          {showDirections && (routeDistance || routeDuration) && (
            <View className="absolute top-4 left-4 right-4 bg-white rounded-lg p-3 shadow-lg">
              <View className="flex-row justify-between items-center">
                <View>
                  {routeDistance && (
                    <Text className="font-semibold text-[16px] text-[#00660A]">
                      Distance: {routeDistance}
                    </Text>
                  )}
                  {routeDuration && (
                    <Text className="text-[14px] text-gray-600">
                      Duration: {routeDuration}
                    </Text>
                  )}
                  {routeType && (
                    <Text className="text-[12px] text-gray-500 italic">
                      {routeType}
                    </Text>
                  )}
                </View>
                <TouchableOpacity
                  onPress={() => setShowDirections(false)}
                  className="bg-gray-200 rounded-full p-2"
                >
                  <Text className="text-[12px] font-semibold">✕</Text>
                </TouchableOpacity>
              </View>
            </View>
          )}
        </View>
        <View className="mt-6 px-4 flex-row items-start justify-between">
          <View
            className="flex-row space-x-6 flex-1"
            style={{
              alignItems: "flex-start",
            }}
          >
            <View className="">
              <LocationIcon />
            </View>
            <View className="space-y-4 flex-1">
              <Text className="">{receiver_name}</Text>
              <View className="flex-row">
                <Text className="leading-5 flex-1">{address}</Text>
              </View>
              <View className="flex-row items-center space-x-6 mt-4">
                <PhoneIcon />
                <MessageIcon />
              </View>
            </View>
          </View>
          <TouchableOpacity
            className="items-center justify-center space-y-2"
            onPress={() => {
              Alert.alert(
                "Directions",
                "Choose how you want to get directions:",
                [
                  {
                    text: "Show Route on Map",
                    onPress: getDirections,
                  },
                  {
                    text: "Open in Navigation App",
                    onPress: openExternalNavigation,
                  },
                  {
                    text: "Cancel",
                    style: "cancel",
                  },
                ]
              );
            }}
          >
            <DirectionsIcon />
            <Text className="text-[#00660A] font-semibold">Get Directions</Text>
          </TouchableOpacity>
        </View>

        <PopUpComponent
          modle={modle}
          setmodle={setmodle}
          text={"DropOff"}
          onpressfun={() => {
            router.push({
              // pathname: "home/trackorder/dropoffmap/camara",
              pathname: "/home/<USER>/dropoffmap/videopage",
              params: {
                id: user_Id,
                invoice_no: invoice_no,
                orderNumber: orderNumber,
                from: "drop",
              },
            });
          }}
        />
      </ScrollView>
      <View className="my-4 px-4">
        <TouchableOpacity
          onPress={() => {
            setmodle(true);
          }}
          className="h-[44px] items-center justify-center bg-[#00660A] rounded-[4px]"
        >
          <Text className="font-[400] font-Pop text-[16px] leading-[24px] text-[#fff]">
            Reached DropOff Location
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

export default index;
