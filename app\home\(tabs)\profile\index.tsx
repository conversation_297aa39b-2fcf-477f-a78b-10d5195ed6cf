import Abouticon from "@/assets/ProfileAsser/SectionIcon/about.svg";
import AccountIcon from "../../../../assets/Hometab/walwt.svg";
import BellIcon from "@/assets/ProfileAsser/SectionIcon/brll.svg";
import EarnIcom from "../../../../assets/Hometab/Earnings.svg";
import HelpIcon from "@/assets/ProfileAsser/SectionIcon/hrlp.svg";
import LearnIcon from "../../../../assets/Hometab/Learn.svg";
import LoginHistoryIcon from "../../../../assets/Hometab/Login History.svg";
import LogoutIcon from "@/assets/ProfileAsser/SectionIcon/logout.svg";
import MoneyIcon from "../../../../assets/Hometab/Money.svg";
import PdfIcon from "@/assets/ProfileAsser/SectionIcon/PdfIcon.svg";
import React from "react";
import Referimg from "@/assets/ProfileAsser/SectionIcon/Refer.png";
import RightArrow from "@/assets/ProfileAsser/RightArrow.svg";
import RightArrow2 from "@/assets/ProfileAsser/SectionIcon/rightarrow.svg";
import RsIcon from "../../../../assets/Hometab/rs.svg";
import StarIcon from "@/assets/ProfileAsser/SectionIcon/star.svg";
import Updateicon from "@/assets/ProfileAsser/SectionIcon/updateIcon.svg";
import UserIcon from "@/assets/ProfileAsser/Profile.svg";
import useGetApiData from "../../../../hooks/useGetApiData";
import useGetDataApi from "../../../../hooks/useGetDataApi";
import useTenStackHook from "@/hooks/TenStackHook/TenStackHook";
import { useQueryClient } from "@tanstack/react-query";
import { router } from "expo-router";
import { Image, ScrollView, Text, TouchableOpacity, View } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useLogin } from "@/store";

const index = () => {
  const { removeuserToken } = useLogin((state) => state);
  const { data, isFetching: isLoading } = useTenStackHook<
    {},
    { data: UserEntity }
  >({
    key: "getProfile",
    canSave: true,
    endpoint: "/getProfile",
  });
  const {
    data: WalletData,
    isLoading: WalletIsLoading,
    triggerreFresh: WalletRefresh,
  } = useGetApiData({
    endpoint: "wallet_balance",
  });
  const queryClient = useQueryClient();

  // Method 1: Clear all queries
  const clearAllQueries = () => {
    queryClient.invalidateQueries({ queryKey: ["status"] });
    queryClient.clear();
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
      }}
    >
      <ScrollView>
        {isLoading ? (
          <></>
        ) : (
          <>
            <View className="px-10 mt-8 flex-row items-center h-[115px] justify-start pr-8 space-x-5 border-[1px] border-[#E9EAE9] m-4">
              <View>
                {data?.data?.image && (
                  <Image
                    source={{ uri: data?.data?.image }}
                    className="w-[60px] h-[60px] rounded-full"
                  />
                )}
              </View>
              <View>
                <View>
                  <Text className="font-[600] text-[20px] leading-[30px]">
                    {data?.data?.name ? data?.data?.name : "UserNama"}
                  </Text>
                </View>
                <View>
                  <Text className="font-[400] text-[16px] leading-[24px] text-[#627164]">
                    {data?.data?.email ? data?.data?.email : "Email Id"}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => {
                    router.push("/home/<USER>/myprofile");
                  }}
                  className="flex-row items-center space-x-1"
                >
                  <Text className="font-[400] leading-[21px] text-[14px] text-[#00660A]">
                    Edit Profile
                  </Text>
                  <RightArrow />
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}
        <View className="mt-4 px-4">
          {/* <TouchableOpacity className="flex-row items-center justify-between p-3 border-[1px] border-[#E9EAE9] rounded-[4px]">
            <View className="flex-row items-center space-x-2">
              <Updateicon />
              <View>
                <Text className="font-[500] text-[#000] leading-[21px] text-[14px]">
                  App update available
                </Text>
              </View>
            </View>
            <View className="flex-row items-center space-x-2">
              <View className="h-[26px] w-[80px] bg-[#FFD5BD] items-center justify-center rounded-full">
                <Text className="text-[12px] font-[600] leading-[18px] text-[#E55300]">
                  v17.64.0
                </Text>
              </View>
              <RightArrow2 />
            </View>
          </TouchableOpacity> */}
        </View>
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Your Rating"}
            icon={<StarIcon />}
            pressfun={() => {
              router.push("/home/<USER>/reting");
            }}
          />
        </View>
        <View className="mt-4 px-4">
          <View className="flex-row items-center justify-between p-3 border-[1px] border-[#E9EAE9] rounded-[4px] bg-[#ECFFEE]">
            {WalletIsLoading ? (
              <></>
            ) : (
              <>
                <View className="flex-row items-center space-x-2">
                  <MoneyIcon />
                  <View className="flex-row items-center space-x-1">
                    <RsIcon />
                    <Text className="font-[500] text-[#000] leading-[21px] text-[14px]">
                      {WalletData?.data}
                    </Text>
                  </View>
                </View>
              </>
            )}

            <View className="flex-row items-center space-x-2">
              <TouchableOpacity
                onPress={() => {
                  router.push("/home/<USER>/wallet");
                }}
                className="h-[26px] w-[101px] bg-[#00660A] items-center justify-center rounded-full"
              >
                <Text className="text-[12px] font-[600] leading-[18px] text-[#fff]">
                  Add Money
                </Text>
              </TouchableOpacity>
              <RightArrow2 />
            </View>
          </View>
        </View>
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Your Earnings"}
            icon={<EarnIcom />}
            pressfun={() => {
              router.push("/home/<USER>/yourearnings");
            }}
          />
        </View>
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Past Orders"}
            icon={<PdfIcon />}
            pressfun={() => {
              router.push("/home/<USER>/pastorders");
            }}
          />
        </View>
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Account Details"}
            icon={<AccountIcon />}
            pressfun={() => {
              router.push("/home/<USER>/accountdetails");
            }}
          />
        </View>
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Learn with us"}
            icon={<LearnIcon />}
            pressfun={() => {
              router.push("/home/<USER>/learn");
            }}
          />
        </View>
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Notifications"}
            icon={<BellIcon />}
            pressfun={() => {
              router.push("/home/<USER>/notificationpage");
            }}
          />
        </View>
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Refer & Earn Program"}
            icon={<Image source={Referimg} />}
            activeOpacity={0.2}
            Opacity={0.2}
          />
          <View className="px-4 mt-4">
            <Text className="font-[500] text-[16px] leading-[24px]">More</Text>
          </View>
        </View>
        {/* <View className="mt-4 px-4">
          <SectionComponent
            text={"Help Centre"}
            icon={<HelpIcon />}
            pressfun={() => {
              router.push("/home/<USER>/help");
            }}
          />
        </View> */}
        <View className="mt-4 px-4">
          <SectionComponent
            text={"Login History"}
            icon={<LoginHistoryIcon />}
            pressfun={() => {
              router.push("/home/<USER>/loginhistory");
            }}
          />
        </View>
        {/* <View className="mt-4 px-4">
          <SectionComponent
            text={"About"}
            icon={<Abouticon />}
            pressfun={() => {
              router.push("home/profile/about");
            }}
          />
        </View> */}
        <View className="mt-4 px-4 mb-4">
          <SectionComponent
            text={"Logout"}
            icon={<LogoutIcon />}
            pressfun={() => {
              clearAllQueries();
              removeuserToken();
              router.dismissAll();
              router.replace("/auth/carouselpage");
            }}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export const SectionComponent = ({
  text,
  icon,
  pressfun,
  activeOpacity,
  Opacity,
}: any) => {
  return (
    <>
      <TouchableOpacity
        onPress={pressfun}
        activeOpacity={activeOpacity}
        style={{
          opacity: Opacity,
        }}
        className="flex-row items-center justify-between p-3 border-[1px] border-[#E9EAE9] rounded-[4px]"
      >
        <View className="flex-row items-center space-x-2">
          {icon}
          <View>
            <Text className="font-[500] text-[#000] leading-[21px] text-[14px]">
              {text}
            </Text>
          </View>
        </View>
        <View className="flex-row items-center space-x-2">
          <RightArrow2 />
        </View>
      </TouchableOpacity>
    </>
  );
};

export default index;
