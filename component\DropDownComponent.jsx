import React from "react";
import { Controller } from "react-hook-form";
import { Text, View } from "react-native";
import { Dropdown } from "react-native-element-dropdown";

const DropDownComponent = ({
  text,
  placeholder,
  style,
  data,
  defaul,
  control,
  name,
  rules,
  setvaluefun,
}) => {
  // Determine the field names based on the data structure
  const getLabelField = () => {
    if (data && data.length > 0) {
      const firstItem = data[0];
      if (firstItem.label !== undefined) return "label";
      if (firstItem.name !== undefined) return "name";
    }
    return "label"; // default fallback
  };

  const getValueField = () => {
    if (data && data.length > 0) {
      const firstItem = data[0];
      if (firstItem.value !== undefined) return "value";
      if (firstItem.name !== undefined) return "name";
    }
    return "value"; // default fallback
  };

  const labelField = getLabelField();
  const valueField = getValueField();

  return (
    <View className="mt-4">
      <Text className="font-[400] text-[16px] text-[#4D4D4D]">{text}</Text>
      <Controller
        name={name}
        control={control}
        rules={{ ...rules }}
        defaultValue={defaul}
        render={({ field: { onChange, value } }) => {
          return (
            <>
              <Dropdown
                data={data}
                placeholder={placeholder}
                placeholderStyle={{
                  color: "#B3B3B3",
                  height: 35,
                  textAlignVertical: "center",
                }}
                style={{
                  minHeight: 40,
                }}
                labelField={labelField}
                valueField={valueField}
                value={value}
                onChange={(item) => {
                  const selectedValue = item[valueField];
                  onChange(selectedValue);
                  setvaluefun && setvaluefun(name, selectedValue);
                }}
                className="border-[1px] border-[#ACB9D5] rounded-[4px] px-3 mt-2"
              />
            </>
          );
        }}
      />
    </View>
  );
};

export default DropDownComponent;
